<?php
/**
 * BitVoucher API
 * API endpoint for voucher operations
 *
 * Supported operations:
 * 1. Get voucher info: ?token=TOKEN&code=CODE
 * 2. Transfer to account: ?token=TOKEN&bv-code=CODE&action=transmission
 * 3. Transfer to wallet: ?token=TOKEN&bv-code=CODE&action=wallet
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include database class
require_once 'JsonDatabase.php';

// Initialize database
$db = new JsonDatabase('data');

/**
 * Send JSON response
 */
function sendResponse($data, $httpCode = 200) {
    http_response_code($httpCode);
    echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * Validate token and get user
 */
function validateToken($db, $token) {
    if (empty($token)) {
        sendResponse([
            'status' => 'error',
            'message' => 'توکن الزامی است'
        ], 400);
    }

    $user = $db->getUserByToken($token);
    if (!$user) {
        sendResponse([
            'status' => 'error',
            'message' => 'توکن نامعتبر است'
        ], 401);
    }

    return $user;
}

/**
 * Get voucher by code (supports both old and new format)
 */
function getVoucherByCode($db, $code) {
    // First try the old format (individual files)
    $voucherFile = "data/vouchers/{$code}.json";
    if (file_exists($voucherFile)) {
        $voucher = json_decode(file_get_contents($voucherFile), true);
        if ($voucher) {
            return $voucher;
        }
    }

    // Then try the new format (JsonDatabase)
    $vouchers = $db->getAll('vouchers');
    foreach ($vouchers as $voucher) {
        if (isset($voucher['voucher_code']) && $voucher['voucher_code'] === $code) {
            return $voucher;
        }
    }

    return null;
}

/**
 * Update voucher status (supports both old and new format)
 */
function updateVoucher($db, $voucher, $updateData) {
    $voucherCode = $voucher['voucher_code'];

    // Update old format file if it exists
    $voucherFile = "data/vouchers/{$voucherCode}.json";
    if (file_exists($voucherFile)) {
        $voucherData = json_decode(file_get_contents($voucherFile), true);
        $voucherData = array_merge($voucherData, $updateData);
        file_put_contents($voucherFile, json_encode($voucherData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    // Update new format if voucher exists in database
    if (isset($voucher['id'])) {
        $db->update('vouchers', $voucher['id'], $updateData);
    }

    return true;
}

// Get request parameters
$token = $_GET['token'] ?? $_POST['token'] ?? '';
$code = $_GET['code'] ?? $_POST['code'] ?? '';
$bvCode = $_GET['bv-code'] ?? $_POST['bv-code'] ?? '';
$action = $_GET['action'] ?? $_POST['action'] ?? '';

// Validate token
$user = validateToken($db, $token);

// Determine operation type
if (!empty($code)) {
    // Operation 1: Get voucher information
    $voucher = getVoucherByCode($db, $code);

    if (!$voucher) {
        sendResponse([
            'status' => 'error',
            'message' => 'کد ووچر یافت نشد'
        ], 404);
    }

    // Return voucher information
    $response = [
        'status' => $voucher['is_used'] ? 'used' : 'active',
        'is_used' => $voucher['is_used'],
        'voucher_code' => $voucher['voucher_code'],
        'amount_toman' => $voucher['amount_toman'],
        'creator_user_id' => $voucher['creator_user_id']
    ];

    sendResponse($response);

} elseif (!empty($bvCode) && !empty($action)) {
    // Operations 2 & 3: Transfer voucher
    $voucher = getVoucherByCode($db, $bvCode);

    if (!$voucher) {
        sendResponse([
            'status' => 'error',
            'message' => 'کد ووچر یافت نشد'
        ], 404);
    }

    if ($voucher['is_used']) {
        sendResponse([
            'status' => 'error',
            'message' => 'کد ووچر نامعتبر یا استفاده شده است'
        ], 400);
    }

    // Mark voucher as used
    $updateData = [
        'is_used' => true,
        'used_by' => $user['id'],
        'used_at' => date('Y-m-d H:i:s'),
        'used_timestamp' => time(),
        'status' => 'used'
    ];

    if (!updateVoucher($db, $voucher, $updateData)) {
        sendResponse([
            'status' => 'error',
            'message' => 'خطا در به‌روزرسانی کد ووچر'
        ], 500);
    }

    if ($action === 'transmission') {
        // Transfer to account balance
        $currentBalance = $db->getUserBalance($user['id']);
        $newBalance = $currentBalance + $voucher['amount_toman'];

        // Update user balance
        $db->updateUserBalance($user['id'], $newBalance);

        // Save transaction
        $transactionId = 'TXN-' . time() . '-' . $user['id'];
        $db->saveTransaction($transactionId, [
            'user_id' => $user['id'],
            'voucher_code' => $voucher['voucher_code'],
            'amount' => $voucher['amount_toman'],
            'currency' => 'IRR',
            'type' => 'voucher_redeem_account',
            'payment_method' => 'voucher',
            'status' => 'completed',
            'created_at' => date('Y-m-d H:i:s'),
            'created_timestamp' => time(),
            'description' => 'انتقال کد ووچر به حساب'
        ]);

        sendResponse([
            'status' => 'success',
            'message' => 'کد ووچر با موفقیت به حساب شما منتقل شد',
            'amount' => $voucher['amount_toman'],
            'new_balance' => $newBalance
        ]);

    } elseif ($action === 'wallet') {
        // Transfer to wallet
        $currentWalletBalance = $db->getUserWalletBalance($user['id']);
        $newWalletBalance = $currentWalletBalance + $voucher['amount_toman'];

        // Update wallet balance
        $db->updateUserWalletBalance($user['id'], $newWalletBalance);

        // Save transaction
        $transactionId = 'TXN-' . time() . '-' . $user['id'];
        $db->saveTransaction($transactionId, [
            'user_id' => $user['id'],
            'voucher_code' => $voucher['voucher_code'],
            'amount' => $voucher['amount_toman'],
            'currency' => 'IRR',
            'type' => 'voucher_redeem_wallet',
            'payment_method' => 'voucher',
            'status' => 'completed',
            'created_at' => date('Y-m-d H:i:s'),
            'created_timestamp' => time(),
            'description' => 'انتقال کد ووچر به کیف پول'
        ]);

        sendResponse([
            'status' => 'success',
            'message' => 'کد ووچر با موفقیت به کیف پول اضافه شد',
            'amount' => $voucher['amount_toman'],
            'wallet_balance' => $newWalletBalance
        ]);

    } else {
        sendResponse([
            'status' => 'error',
            'message' => 'عملیات نامعتبر است'
        ], 400);
    }

} else {
    // Invalid request
    sendResponse([
        'status' => 'error',
        'message' => 'پارامترهای درخواست نامعتبر است',
        'usage' => [
            'get_voucher_info' => 'api.php?token=TOKEN&code=CODE',
            'transfer_to_account' => 'api.php?token=TOKEN&bv-code=CODE&action=transmission',
            'transfer_to_wallet' => 'api.php?token=TOKEN&bv-code=CODE&action=wallet'
        ]
    ], 400);
}
?>
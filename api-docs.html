<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitVoucher API Documentation</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @font-face {
            font-family: 'IRANSans';
            src: url('irsans.ttf') format('truetype');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'IRANSans', 'Tahoma', 'Arial', sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            color: #e0e6ed;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(45deg, #ffffff, #5a67d8, #667eea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            color: #a0aec0;
            font-size: 1.1rem;
        }

        .api-section {
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 15px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.08);
        }

        .api-title {
            font-size: 1.5rem;
            color: #ffffff;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .api-url {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            color: #ffffff;
            word-break: break-all;
            position: relative;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 212, 255, 0.2);
            border: none;
            color: #ffffff;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            font-family: 'IRANSans', 'Tahoma', 'Arial', sans-serif;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            background: rgba(0, 212, 255, 0.4);
        }

        .response-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .response-box {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .response-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.1);
            border-color: rgba(0, 212, 255, 0.3);
        }

        .response-title {
            font-size: 1.1rem;
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 8px;
            font-weight: bold;
        }

        .success {
            background: rgba(72, 187, 120, 0.2);
            color: #68d391;
            border: 1px solid rgba(72, 187, 120, 0.3);
        }

        .error {
            background: rgba(245, 101, 101, 0.2);
            color: #fc8181;
            border: 1px solid rgba(245, 101, 101, 0.3);
        }

        .used {
            background: rgba(237, 137, 54, 0.2);
            color: #ed8936;
            border: 1px solid rgba(237, 137, 54, 0.3);
        }

        .json-code {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            overflow-x: auto;
            white-space: pre;
        }

        .json-key {
            color: #79b8ff;
        }

        .json-string {
            color: #85e89d;
        }

        .json-number {
            color: #f97583;
        }

        .json-boolean {
            color: #ffab70;
        }

        .description {
            margin-top: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-right: 4px solid #ffffff;
            color: #cbd5e0;
            line-height: 1.6;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #718096;
        }

        /* FAB Styles */
        .fab {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 200px;
            height: 60px;
            background: linear-gradient(45deg, #3f51b5, #5a67d8);
            color: white;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-family: 'IRANSans', 'Tahoma', 'Arial', sans-serif;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 8px 25px rgba(63, 81, 181, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            opacity: 0;
            transform: translateY(100px);
            animation: fabSlideIn 1s ease-out 2s forwards;
        }

        .fab:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(63, 81, 181, 0.4);
            background: linear-gradient(45deg, #5a67d8, #3f51b5);
        }

        .fab:active {
            transform: translateY(-2px);
        }

        .fab i {
            font-size: 18px;
            margin-left: 5px;
        }

        .fab span {
            font-size: 14px;
            font-weight: bold;
            white-space: nowrap;
        }

        @keyframes fabSlideIn {
            0% {
                opacity: 0;
                transform: translateY(100px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fab-close {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .fab-close:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                padding: 15px;
                border-radius: 15px;
                margin: 0;
                max-width: 100%;
            }

            .header {
                margin-bottom: 25px;
            }

            .header h1 {
                font-size: 1.8rem;
                line-height: 1.2;
            }

            .header p {
                font-size: 1rem;
                line-height: 1.5;
            }

            .api-section {
                padding: 20px;
                border-radius: 12px;
                margin-bottom: 25px;
            }

            .api-title {
                font-size: 1.2rem;
                line-height: 1.3;
                margin-bottom: 12px;
            }

            .api-url {
                padding: 12px;
                border-radius: 8px;
                font-size: 12px;
                word-break: break-all;
                line-height: 1.4;
                margin: 12px 0;
            }

            .copy-btn {
                top: 8px;
                left: 8px;
                padding: 4px 8px;
                font-size: 11px;
                border-radius: 4px;
            }

            .response-container {
                grid-template-columns: 1fr;
                gap: 15px;
                margin-top: 15px;
            }

            .response-box {
                padding: 15px;
                border-radius: 10px;
            }

            .response-title {
                font-size: 1rem;
                margin-bottom: 8px;
                padding: 6px 10px;
                border-radius: 6px;
            }

            .json-code {
                padding: 12px;
                border-radius: 6px;
                font-size: 12px;
                line-height: 1.5;
            }

            .description {
                margin-top: 12px;
                padding: 12px;
                border-radius: 6px;
                font-size: 13px;
                line-height: 1.5;
            }

            .fab {
                width: 160px;
                height: 50px;
                bottom: 20px;
                right: 20px;
                border-radius: 25px;
                font-size: 12px;
            }

            .fab span {
                font-size: 12px;
            }

            .fab i {
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 5px;
            }

            .container {
                padding: 12px;
                border-radius: 12px;
            }

            .header h1 {
                font-size: 1.6rem;
            }

            .header p {
                font-size: 0.9rem;
            }

            .api-section {
                padding: 15px;
                margin-bottom: 20px;
            }

            .api-title {
                font-size: 1.1rem;
                margin-bottom: 10px;
            }

            .api-url {
                padding: 10px;
                font-size: 11px;
                margin: 10px 0;
            }

            .copy-btn {
                top: 6px;
                left: 6px;
                padding: 3px 6px;
                font-size: 10px;
            }

            .response-container {
                gap: 12px;
                margin-top: 12px;
            }

            .response-box {
                padding: 12px;
            }

            .response-title {
                font-size: 0.9rem;
                margin-bottom: 6px;
                padding: 5px 8px;
            }

            .json-code {
                padding: 10px;
                font-size: 11px;
                line-height: 1.4;
            }

            .description {
                margin-top: 10px;
                padding: 10px;
                font-size: 12px;
                line-height: 1.4;
            }

            .fab {
                width: 140px;
                height: 45px;
                bottom: 15px;
                right: 15px;
                border-radius: 22px;
                font-size: 11px;
            }

            .fab span {
                font-size: 11px;
            }

            .fab i {
                font-size: 12px;
            }
        }

        /* Landscape orientation for mobile */
        @media (max-width: 768px) and (orientation: landscape) {
            .container {
                padding: 10px;
            }

            .header {
                margin-bottom: 20px;
            }

            .api-section {
                padding: 15px;
                margin-bottom: 20px;
            }

            .fab {
                width: 120px;
                height: 40px;
                bottom: 10px;
                right: 10px;
                font-size: 10px;
            }
        }

        /* Touch-friendly improvements */
        @media (hover: none) and (pointer: coarse) {
            .copy-btn {
                min-height: 32px;
                min-width: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .fab {
                min-height: 44px; /* Apple's recommended touch target size */
            }
        }

        /* High DPI displays */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .json-code {
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-book"></i> مستندات فنی</h1>
            <p>مستندات کامل API صرافی بیت ووچر</p>
        </div>



        <div class="api-section">
            <div class="api-title">
                <i class="fas fa-search"></i> نحوه گرفتن اطلاعات کد ووچر
            </div>
            <div class="api-url">
                <button class="copy-btn" onclick="copyToClipboard(this)">کپی</button>
                https://speedx-team.ir/BIT/api?token=TOKEN&code=CODE
            </div>
            <div class="description">
                <strong>پارامترها:</strong><br>
                • <code>token</code>: توکن شما از بخش "<i class="fas fa-eye"></i> مشاهده توکن"<br>
                • <code>code</code>: کد ووچر مورد نظر (مثال: BV-1A7D-7EAE-F261-24DD)
            </div>

            <div class="response-container">
                <div class="response-box">
                    <div class="response-title success"><i class="fas fa-check-circle"></i> کد ووچر معتبر و فعال</div>
                    <div class="json-code">{
  <span class="json-key">"status"</span>: <span class="json-string">"active"</span>,
  <span class="json-key">"is_used"</span>: <span class="json-boolean">false</span>,
  <span class="json-key">"voucher_code"</span>: <span class="json-string">"BV-1A7D-7EAE-F261-24DD"</span>,
  <span class="json-key">"amount_toman"</span>: <span class="json-number">20000</span>,
  <span class="json-key">"creator_user_id"</span>: <span class="json-string">"2038958340"</span>
}</div>
                    <div class="description">
                        این پاسخ زمانی برگردانده می‌شود که کد ووچر معتبر و هنوز استفاده نشده باشد.<br><br>
                        <strong>توضیح پارامترهای خروجی:</strong><br>
                        • <code>amount_toman</code>: مقدار کد ووچر به تومان<br>
                        • <code>creator_user_id</code>: شناسه کاربری سازنده کد ووچر
                    </div>
                </div>

                <div class="response-box">
                    <div class="response-title used"><i class="fas fa-exclamation-triangle"></i> کد ووچر استفاده شده</div>
                    <div class="json-code">{
  <span class="json-key">"status"</span>: <span class="json-string">"used"</span>,
  <span class="json-key">"is_used"</span>: <span class="json-boolean">true</span>,
  <span class="json-key">"voucher_code"</span>: <span class="json-string">"BV-1A7D-7EAE-F261-24DD"</span>,
  <span class="json-key">"amount_toman"</span>: <span class="json-number">20000</span>,
  <span class="json-key">"creator_user_id"</span>: <span class="json-string">"2038958340"</span>
}</div>
                    <div class="description">
                        این پاسخ زمانی برگردانده می‌شود که کد ووچر معتبر است اما قبلاً استفاده شده است.<br><br>
                        <strong>توضیح پارامترهای خروجی:</strong><br>
                        • <code>amount_toman</code>: مقدار کد ووچر به تومان<br>
                        • <code>creator_user_id</code>: شناسه کاربری سازنده کد ووچر
                    </div>
                </div>

                <div class="response-box">
                    <div class="response-title error"><i class="fas fa-times-circle"></i> کد ووچر نامعتبر</div>
                    <div class="json-code">{
  <span class="json-key">"status"</span>: <span class="json-string">"error"</span>,
  <span class="json-key">"message"</span>: <span class="json-string">"کد ووچر یافت نشد"</span>
}</div>
                    <div class="description">
                        این پاسخ زمانی برگردانده می‌شود که کد ووچر وارد شده در سیستم وجود ندارد.
                    </div>
                </div>
            </div>
        </div>


        <div class="api-section">
            <div class="api-title">
                <i class="fas fa-wallet"></i> نحوه انتقال کد ووچر به کیف پول شما
            </div>
            <div class="api-url">
                <button class="copy-btn" onclick="copyToClipboard(this)">کپی</button>
                https://speedx-team.ir/BIT/api?token=TOKEN&bv-code=CODE&action=wallet
            </div>
            <div class="description">
                <strong>پارامترها:</strong><br>
                • <code>token</code>: توکن شما از بخش "<i class="fas fa-eye"></i> مشاهده توکن"<br>
                • <code>bv-code</code>: کد ووچر مورد نظر<br>
                • <code>action</code>: wallet (انتقال به کیف پول)
            </div>

            <div class="response-container">
                <div class="response-box">
                    <div class="response-title success"><i class="fas fa-check-circle"></i> انتقال موفق به کیف پول</div>
                    <div class="json-code">{
  <span class="json-key">"status"</span>: <span class="json-string">"success"</span>,
  <span class="json-key">"message"</span>: <span class="json-string">"کد ووچر با موفقیت به کیف پول اضافه شد"</span>,
  <span class="json-key">"amount"</span>: <span class="json-number">20000</span>,
  <span class="json-key">"wallet_balance"</span>: <span class="json-number">65000</span>
}</div>
                    <div class="description">
                        کد ووچر با موفقیت به کیف پول شما اضافه شد و موجودی جدید کیف پول نمایش داده شده است.<br><br>
                        <strong>توضیح پارامترهای خروجی:</strong><br>
                        • <code>amount</code>: مقدار کد ووچر اضافه شده به تومان<br>
                        • <code>wallet_balance</code>: موجودی جدید کیف پول شما پس از اضافه شدن
                    </div>
                </div>

                <div class="response-box">
                    <div class="response-title error"><i class="fas fa-times-circle"></i> خطا در انتقال</div>
                    <div class="json-code">{
  <span class="json-key">"status"</span>: <span class="json-string">"error"</span>,
  <span class="json-key">"message"</span>: <span class="json-string">"کد ووچر نامعتبر یا استفاده شده است"</span>
}</div>
                    <div class="description">
                        خطا در انتقال کد ووچر به کیف پول - کد نامعتبر یا قبلاً استفاده شده است.
                    </div>
                </div>
            </div>
        </div>



        <div class="footer">
            <p><i class="fas fa-copyright"></i> 2025 BitVoucher API - تمامی حقوق محفوظ است</p>
        </div>
    </div>

    <!-- FAB Button -->
    <div class="fab" id="installFab" onclick="handleInstallClick()">
        <button class="fab-close" onclick="closeFab(event)">&times;</button>
        <span>راه اندازی و نصب رایگان</span>
    </div>

    <script>
        function copyToClipboard(button) {
            const text = button.parentElement.textContent.replace('کپی', '').trim();
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.textContent;
                button.textContent = '✓ کپی شد';
                button.style.background = 'rgba(72, 187, 120, 0.3)';
                button.style.color = '#68d391';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'rgba(0, 212, 255, 0.2)';
                    button.style.color = '#ffffff';
                }, 2000);
            });
        }

        function handleInstallClick() {
            // باز کردن صفحه جدید برای جمع‌آوری اطلاعات کسب و کار
            window.open('business-info.html', '_blank');
        }

        function closeFab(event) {
            event.stopPropagation();
            const fab = document.getElementById('installFab');
            fab.style.animation = 'fabSlideOut 0.3s ease-in forwards';

            setTimeout(() => {
                fab.style.display = 'none';
            }, 300);
        }

        // Add slide out animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fabSlideOut {
                0% {
                    opacity: 1;
                    transform: translateY(0);
                }
                100% {
                    opacity: 0;
                    transform: translateY(100px);
                }
            }
        `;
        document.head.appendChild(style);

        // Add smooth scroll animation
        document.addEventListener('DOMContentLoaded', function() {
            const responseBoxes = document.querySelectorAll('.response-box');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            responseBoxes.forEach(box => {
                box.style.opacity = '0';
                box.style.transform = 'translateY(20px)';
                box.style.transition = 'all 0.6s ease';
                observer.observe(box);
            });
        });

        // Add typing animation to JSON
        function typeJSON(element, json, speed = 50) {
            element.innerHTML = '';
            let i = 0;
            
            function typeWriter() {
                if (i < json.length) {
                    element.innerHTML += json.charAt(i);
                    i++;
                    setTimeout(typeWriter, speed);
                }
            }
            
            typeWriter();
        }
    </script>
</body>
</html>

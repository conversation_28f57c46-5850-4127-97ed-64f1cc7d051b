# BitVoucher API .htaccess Configuration
RewriteEngine On

# API URL Rewriting - REDIRECT TO BIT FOLDER
# Redirect API calls to the BIT folder
RewriteRule ^api$ /BIT/api [QSA,R=301,L]
RewriteRule ^api/$ /BIT/api [QSA,R=301,L]

# Test API endpoint
RewriteRule ^test-api$ test-api.php [QSA,L]

# Other specific endpoints
RewriteRule ^docs$ api-docs.html [L]
RewriteRule ^business$ business-info.html [L]
RewriteRule ^callback$ callback.php [QSA,L]
RewriteRule ^bot$ bot.php [QSA,L]

# General PHP file handling (without extension)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^([^.]+)$ $1.php [L]

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# CORS Headers for API
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"

# Handle preflight OPTIONS requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]



# Security: Block access to sensitive files
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "JsonDatabase.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "Mmb.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "jdf.php">
    Order Allow,Deny
    Deny from all
</Files>

# Block access to data directory
<IfModule mod_rewrite.c>
    RewriteRule ^data/ - [F,L]
</IfModule>

# Block access to log files
<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# Block access to JSON data files
<Files "*.json">
    Order Allow,Deny
    Deny from all
</Files>

# Allow specific files
<Files "api.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "callback.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "bot.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "api-docs.html">
    Order Allow,Deny
    Allow from all
</Files>

<Files "business-info.html">
    Order Allow,Deny
    Allow from all
</Files>

<Files "irsans.ttf">
    Order Allow,Deny
    Allow from all
</Files>

# Compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache control for static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType application/json "access plus 0 seconds"
</IfModule>

# PHP settings for API
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 128M
    php_flag display_errors Off
    php_flag log_errors On
</IfModule>

# Ensure proper content type for API responses
<Files "api.php">
    Header set Content-Type "application/json; charset=utf-8"
</Files>
